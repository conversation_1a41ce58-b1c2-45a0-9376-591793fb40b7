package com.hll.mapdataservice.business.api.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * Request DTO for data merging API
 * 
 */
@Data
public class DataMergingRequest {
    
    /**
     * Country prefix (e.g., "twn", "bra", "mex")
     * Must match the country codes defined in CountryAreaEnum
     */
    @NotBlank(message = "Country prefix cannot be blank")
    @Pattern(regexp = "^[a-z]{3,5}$", message = "Country prefix must be 3-5 lowercase letters")
    private String countryPrefix;
    
    /**
     * Whether to perform dry run (validation only, no actual merging)
     * Default: false
     */
    private boolean dryRun = false;
    
    /**
     * Whether to continue merging other tables if one fails
     * Default: true (continue on error)
     */
    private boolean continueOnError = true;
    
    /**
     * Batch size for processing (optional, uses default if not specified)
     */
    private Integer batchSize;
    
    /**
     * Timeout in seconds for the entire operation (optional)
     */
    private Integer timeoutSeconds;
}
