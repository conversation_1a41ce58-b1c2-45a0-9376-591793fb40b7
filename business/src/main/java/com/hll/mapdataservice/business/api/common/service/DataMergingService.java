package com.hll.mapdataservice.business.api.common.service;

import com.hll.mapdataservice.business.api.common.dto.DataMergingRequest;
import com.hll.mapdataservice.business.api.common.dto.DataMergingResponse;

/**
 * Service interface for automated data merging operations
 * Handles merging of partition tables into consolidated tables
 * 
 */
public interface DataMergingService {
    
    /**
     * Merge partition tables into consolidated tables for a given country
     * 
     * @param request The merging request containing country prefix and options
     * @return Response containing detailed results of the merging operation
     */
    DataMergingResponse mergePartitionData(DataMergingRequest request);
    
    /**
     * Validate that the country prefix is supported and has partition configuration
     * 
     * @param countryPrefix The country prefix to validate
     * @return true if the country is supported, false otherwise
     */
    boolean isCountrySupported(String countryPrefix);
    
    /**
     * Get the list of areas/partitions for a given country
     * 
     * @param countryPrefix The country prefix
     * @return List of area names (e.g., ["area1", "area2", "area3"])
     */
    java.util.List<String> getCountryAreas(String countryPrefix);
}
