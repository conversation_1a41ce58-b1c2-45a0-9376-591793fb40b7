package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.common.dto.DataMergingRequest;
import com.hll.mapdataservice.business.api.common.dto.DataMergingResponse;
import com.hll.mapdataservice.business.api.common.service.DataMergingService;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.CountryAreaEnum;
import com.hll.mapdataservice.common.mapper.DataMergingMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Implementation of DataMergingService
 * Handles automated merging of partition tables into consolidated tables
 *
 */
@Service
@Slf4j
public class DataMergingServiceImpl implements DataMergingService {

    @Resource
    private DataMergingMapper dataMergingMapper;

    /**
     * The 8 table types that need to be merged:
     * 4 material tables (with _m suffix) and 4 product tables (without _m)
     */
    private static final List<String> TABLE_TYPES = Arrays.asList(
            "link_m", "node_m", "relation_m", "rule_m",  // Material tables
            "link", "node", "relation", "rule"           // Product tables
    );

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataMergingResponse mergePartitionData(DataMergingRequest request) {
        DataMergingResponse response = new DataMergingResponse();
        response.setCountryPrefix(request.getCountryPrefix());
        response.setDryRun(request.isDryRun());
        response.setStartTime(LocalDateTime.now());

        try {
            // Validate country support
            if (!isCountrySupported(request.getCountryPrefix())) {
                response.setSuccess(false);
                response.getErrors().add("Unsupported country prefix: " + request.getCountryPrefix());
                return response;
            }

            // Get areas for the country
            List<String> areas = getCountryAreas(request.getCountryPrefix());
            response.setPartitionCount(areas.size());
            response.setProcessedAreas(areas);

            // Set up database context
            setupDatabaseContext(request.getCountryPrefix());

            // Calculate total operations
            int totalOperations = TABLE_TYPES.size();
            response.setTotalMergeOperations(totalOperations);

            // Process each table type
            int successCount = 0;
            System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"+DynamicDataSourceContextHolder.peek());
            for (String tableType : TABLE_TYPES) {
                try {
                    DataMergingResponse.TableMergeResult tableResult =
                            mergeTableType(tableType, areas, request);
                    response.getTableResults().put(tableType, tableResult);

                    if (tableResult.isSuccess()) {
                        successCount++;
                    } else {
                        response.getErrors().add("Failed to merge " + tableType + ": " + tableResult.getErrorMessage());
                        if (!request.isContinueOnError()) {
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("Error merging table type: {}", tableType, e);
                    response.getErrors().add("Error merging " + tableType + ": " + e.getMessage());
                    if (!request.isContinueOnError()) {
                        break;
                    }
                }
            }

            response.setSuccessfulMergeOperations(successCount);
            response.setFailedMergeOperations(totalOperations - successCount);
            response.setSuccess(successCount == totalOperations);

        } catch (Exception e) {
            log.error("Unexpected error during data merging", e);
            response.setSuccess(false);
            response.getErrors().add("Unexpected error: " + e.getMessage());
        } finally {
            response.setEndTime(LocalDateTime.now());
            response.setDurationMs(
                    java.time.Duration.between(response.getStartTime(), response.getEndTime()).toMillis()
            );
        }

        return response;
    }

    @Override
    public boolean isCountrySupported(String countryPrefix) {
        return CountryAreaEnum.getAreaByCountry(countryPrefix) != null;
    }

    @Override
    public List<String> getCountryAreas(String countryPrefix) {
        return CountryAreaEnum.getAreaByCountry(countryPrefix);
    }

    /**
     * Set up database context for the country
     */
    private void setupDatabaseContext(String countryPrefix) {
        String dataSource = CommonUtils.getDsbyCountry(countryPrefix, false);
        DynamicDataSourceContextHolder.push(dataSource);
        MybatisPlusConfig.myTableName.set("");
        log.info("Set database context to: {} for country: {}", dataSource, countryPrefix);
    }

    /**
     * Merge a specific table type from all partitions into consolidated table
     */
    private DataMergingResponse.TableMergeResult mergeTableType(String tableType, List<String> areas,
                                                                DataMergingRequest request) {
        DataMergingResponse.TableMergeResult result = new DataMergingResponse.TableMergeResult();
        result.setTableType(tableType);
        result.setTargetTable(tableType);

        long startTime = System.currentTimeMillis();

        try {
            // Check if target table exists
            if (dataMergingMapper.checkTableExists(tableType) == 0) {
                result.setSuccess(false);
                result.setErrorMessage("Target table does not exist: " + tableType);
                return result;
            }

            int totalRecordsProcessed = 0;
            int totalRecordsInserted = 0;

            // Process each partition
            for (String area : areas) {
                String sourceTable = tableType + "_" + area;
                result.getSourcePartitions().add(sourceTable);

                // Check if source table exists
                if (dataMergingMapper.checkTableExists(sourceTable) == 0) {
                    log.warn("Source table does not exist: {}, skipping", sourceTable);
                    continue;
                }

                // Get record count before merge
                long recordsBefore = dataMergingMapper.getTableRecordCount(tableType);
                long sourceRecords = dataMergingMapper.getTableRecordCount(sourceTable);
                totalRecordsProcessed += sourceRecords;

                if (!request.isDryRun()) {
                    // Perform the merge
                    int insertedRecords = dataMergingMapper.mergePartitionTable(tableType, sourceTable);
                    totalRecordsInserted += insertedRecords;

                    log.info("Merged {} records from {} to {}", insertedRecords, sourceTable, tableType);
                } else {
                    log.info("Dry run: Would merge {} records from {} to {}", sourceRecords, sourceTable, tableType);
                }
            }

            result.setRecordsProcessed(totalRecordsProcessed);
            result.setRecordsInserted(totalRecordsInserted);
            result.setRecordsSkipped(totalRecordsProcessed - totalRecordsInserted);
            result.setSuccess(true);

        } catch (Exception e) {
            log.error("Error merging table type: {}", tableType, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setDurationMs(System.currentTimeMillis() - startTime);
        }

        return result;
    }
}
