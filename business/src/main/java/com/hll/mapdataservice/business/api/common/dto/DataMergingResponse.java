package com.hll.mapdataservice.business.api.common.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Response DTO for data merging API
 * 
 */
@Data
public class DataMergingResponse {
    
    /**
     * Whether the overall operation was successful
     */
    private boolean success;
    
    /**
     * Country prefix that was processed
     */
    private String countryPrefix;
    
    /**
     * Number of partitions processed
     */
    private int partitionCount;
    
    /**
     * List of areas/partitions that were processed
     */
    private List<String> processedAreas = new ArrayList<>();
    
    /**
     * Total number of table merges attempted
     */
    private int totalMergeOperations;
    
    /**
     * Number of successful merge operations
     */
    private int successfulMergeOperations;
    
    /**
     * Number of failed merge operations
     */
    private int failedMergeOperations;
    
    /**
     * Detailed results for each table type
     * Key: table type (e.g., "link_m", "node", etc.)
     * Value: merge result details
     */
    private Map<String, TableMergeResult> tableResults = new HashMap<>();
    
    /**
     * List of error messages if any operations failed
     */
    private List<String> errors = new ArrayList<>();
    
    /**
     * List of warning messages
     */
    private List<String> warnings = new ArrayList<>();
    
    /**
     * Start time of the operation
     */
    private LocalDateTime startTime;
    
    /**
     * End time of the operation
     */
    private LocalDateTime endTime;
    
    /**
     * Duration of the operation in milliseconds
     */
    private long durationMs;
    
    /**
     * Whether this was a dry run
     */
    private boolean dryRun;
    
    /**
     * Additional metadata about the operation
     */
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Nested class for individual table merge results
     */
    @Data
    public static class TableMergeResult {
        private String tableType;
        private boolean success;
        private int recordsProcessed;
        private int recordsInserted;
        private int recordsSkipped; // due to conflicts
        private String errorMessage;
        private long durationMs;
        private List<String> sourcePartitions = new ArrayList<>();
        private String targetTable;
    }
}
