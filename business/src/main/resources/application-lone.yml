# region: hkg,mys,phl,sgp,ban
#         twn,vnm,tha,bra,idn,tur,mex,india
# poi convert:
#         source_db: here_{region}_{year}_{quarter}
#         target_db: map_data_hkg,map_data_mys,map_data_phl,map_data_sgp,map_data_ban
#                    map_data_twn,map_data_vnm,map_data_tha,map_data_bra,map_data_idn,map_data_tur,map_data_mex,map_data_india
#         target_table: poi,poi_diff,poi_fusion_result,poi_raw_{year}_{quarter}
# poi ds:
#         poi,shp
server:
  port: 10050
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: mapdataservice-lone
  redis:
    host: *************
    port: 16379
    password: hll_map_road@2021
    database: 0
  datasource:
    dynamic:
      primary: db1 #设置默认的数据源或者数据源组,默认值即为master
      datasource:
        #phl
        db1:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_phl_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db4:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_phl_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #hkg
        db7:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_hkg_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db9:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_hkg_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #sgp
        db11:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_sgp_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db13:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_sgp_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #mys
        db12:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_mys_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db14:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_mys_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #ban
        db25:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_ban_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db26:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_ban_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #tha
        db2:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_tha_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db5:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_tha_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #twn
        db8:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_twn_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db10:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_twn_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #bra
        db17:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_bra_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db18:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_bra_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #idn
        db15:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/here_ind_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db16:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://*************:15999/hll_oversea_h_ind_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #tur
        db27:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: **********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db28:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: ************************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #vnm
        db3:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: **********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db6:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: ************************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #mex
        db19:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: **********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db20:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: ************************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        #india
        db23:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: ************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
        db24:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: **************************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
third:
  aoibaseservice:
    url: "http://**************:8089"
  roadmatchservice:
    url: "http://***************:8080"
  hereroadmatchservice:
    url: "http://***************:9002"
  databasebackupservice:
    url: "http://**************:10098"
hll:
  client:
    id:
      url: http://*************:8995/
minio:
  endPoint: "http://**************:31112/"
  accessKey: "admin"
  secretKey: "haoyiping"
  bucketName: "test"
