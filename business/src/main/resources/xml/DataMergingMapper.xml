<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.DataMergingMapper">

    <!-- Merge partition table data into consolidated table using PostgreSQL ON CONFLICT -->
    <insert id="mergePartitionTable">
        INSERT INTO ${targetTable}
        SELECT * FROM ${sourceTable}
        ON CONFLICT DO NOTHING
    </insert>

    <!-- Check if a table exists in the current database -->
    <select id="checkTableExists" resultType="int">
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_name = #{tableName}
        AND table_schema = current_schema()
    </select>

    <!-- Get the count of records in a table -->
    <select id="getTableRecordCount" resultType="long">
        SELECT COUNT(*) FROM ${tableName}
    </select>

    <!-- Merge with custom conflict resolution -->
    <insert id="mergeWithCustomConflictResolution">
        INSERT INTO ${targetTable}
        SELECT * FROM ${sourceTable}
        ON CONFLICT (${conflictColumns}) DO NOTHING
    </insert>

</mapper>